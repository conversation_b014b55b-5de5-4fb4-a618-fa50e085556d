# 管理端统一退款审核接口

## 概述

新的统一退款审核接口支持同时处理主订单和追加服务的退款，支持设置不同的退款金额，并可以指定是否退还卡券。

## 接口列表

### 1. 获取订单退款信息

- **接口**: `GET /admin/orders/{orderId}/refund-info`
- **描述**: 获取订单的详细退款信息，包括主订单和所有追加服务的信息
- **参数**: 
  - `orderId` (number): 订单ID

**响应示例**:
```json
{
  "orderId": 123,
  "orderSn": "ORD20240101123456",
  "customer": {
    "id": 1,
    "nickname": "张三",
    "phone": "13800138000"
  },
  "employee": {
    "id": 1,
    "name": "李美容师",
    "phone": "13900139000"
  },
  "summary": {
    "mainOrderAmount": 200.00,
    "additionalServiceCount": 2,
    "additionalServiceAmount": 150.00,
    "totalRefundableAmount": 350.00
  },
  "mainOrder": {
    "type": "main_order",
    "id": "ORD20240101123456",
    "orderSn": "ORD20240101123456",
    "status": "已完成",
    "totalFee": 200.00,
    "originalPrice": 200.00,
    "canRefund": true,
    "refundableAmount": 200.00
  },
  "additionalServices": [
    {
      "type": "additional_service",
      "id": 456,
      "orderSn": "ADD20240101456789",
      "status": "已付款",
      "totalFee": 80.00,
      "originalPrice": 100.00,
      "cardDeduction": 20.00,
      "couponDeduction": 0.00,
      "serviceNames": "精油护理, 深层清洁",
      "hasDiscounts": true,
      "canRefund": true,
      "refundableAmount": 80.00,
      "createdAt": "2024-01-01T10:30:00.000Z"
    }
  ]
}
```

### 2. 统一退款审核

- **接口**: `POST /admin/orders/refund-audit`
- **描述**: 统一处理主订单和追加服务的退款审核
- **请求体**:

```typescript
{
  operatorId: number;                    // 操作员ID（必填）
  result: boolean;                       // 审核结果（必填）
  reason?: string;                       // 审核原因（审核不通过时必填）
  refundItems: Array<{                   // 退款项目列表（必填）
    type: 'main_order' | 'additional_service';  // 退款类型
    id: string | number;                 // 主订单使用sn，追加服务使用id
    refundAmount?: number;               // 退款金额（可选，默认为订单总金额）
    shouldRefundCoupons?: boolean;       // 是否退还卡券（可选，默认true）
  }>;
}
```

**请求示例**:
```json
{
  "operatorId": 1001,
  "result": true,
  "reason": "客户要求退款",
  "refundItems": [
    {
      "type": "main_order",
      "id": "ORD20240101123456",
      "refundAmount": 150.00,
      "shouldRefundCoupons": true
    },
    {
      "type": "additional_service", 
      "id": 456,
      "refundAmount": 80.00,
      "shouldRefundCoupons": false
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "退款审核完成，成功2项，失败0项",
  "auditResult": true,
  "totalRefundAmount": 230.00,
  "results": [
    {
      "type": "main_order",
      "id": "ORD20240101123456",
      "success": true,
      "refundAmount": 150.00,
      "message": "主订单退款成功"
    },
    {
      "type": "additional_service",
      "id": 456,
      "success": true,
      "refundAmount": 80.00,
      "message": "追加服务退款成功"
    }
  ],
  "operator": {
    "id": 1001
  }
}
```

## 业务规则

### 退款金额规则
1. 如果不指定 `refundAmount`，默认退还订单的全部金额
2. 指定的退款金额不能超过订单的实际支付金额
3. 支持部分退款，可以设置任意小于等于订单金额的退款数值

### 卡券退还规则
1. `shouldRefundCoupons` 默认为 `true`，即默认退还卡券
2. 设置为 `false` 时，不退还该订单使用的代金券和权益卡
3. 主订单和追加服务可以分别设置是否退还卡券
4. 只有在退还卡券时，才会增加用户的代金券/权益卡次数

### 审核流程
1. 审核通过 (`result: true`)：执行实际的退款操作
2. 审核不通过 (`result: false`)：只记录审核结果，不执行退款
3. 支持批量审核多个退款项目
4. 如果部分项目退款失败，会在响应中详细说明

### 权限说明
- 管理员可以对任何状态的订单进行退款审核
- 无需检查订单状态，支持强制退款
- 操作会记录在系统日志中，便于追踪

## 错误处理

常见错误码：
- `400`: 参数错误（操作员ID为空、退款项目为空等）
- `404`: 订单不存在
- `500`: 微信退款失败或其他系统错误

错误响应示例：
```json
{
  "success": false,
  "message": "退款审核完成，成功1项，失败1项",
  "results": [
    {
      "type": "main_order",
      "id": "ORD20240101123456",
      "success": true,
      "refundAmount": 150.00,
      "message": "主订单退款成功"
    },
    {
      "type": "additional_service",
      "id": 999,
      "success": false,
      "error": "追加服务订单不存在: 999",
      "message": "追加服务退款失败"
    }
  ]
}
```
